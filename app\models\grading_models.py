"""
Pydantic models cho Auto Grading API
"""

from pydantic import BaseModel, Field
from typing import Dict, List, Optional, Any
from datetime import datetime

class AnswerDetail(BaseModel):
    """Chi tiết một câu trả lời"""
    student_answer: str = Field(..., description="Câu trả lời của học sinh")
    correct_answer: str = Field(..., description="Đáp án đúng")
    is_correct: bool = Field(..., description="Có đúng không")
    is_blank: bool = Field(..., description="Có bỏ trống không")
    confidence: float = Field(..., description="Độ tin cậy (0-1)")
    status: str = Field(..., description="Trạng thái: correct/wrong/blank")

class GradingResultResponse(BaseModel):
    """Response cho kết quả chấm điểm một phiếu"""
    student_id: str = Field(..., description="Số báo danh")
    test_code: str = Field(..., description="Mã đề thi")
    score: float = Field(..., description="Điểm số (thang 10)")
    total_questions: int = Field(..., description="Tổng số câu hỏi")
    correct_answers: int = Field(..., description="Số câu đúng")
    wrong_answers: int = Field(..., description="Số câu sai")
    blank_answers: int = Field(..., description="Số câu bỏ trống")
    answers: Dict[int, str] = Field(..., description="Câu trả lời của học sinh")
    correct_answer_keys: Dict[int, str] = Field(..., description="Đáp án đúng")
    answer_details: Dict[int, AnswerDetail] = Field(..., description="Chi tiết từng câu")
    processed_image_base64: str = Field(..., description="Ảnh đã xử lý (base64)")
    confidence_score: float = Field(..., description="Độ tin cậy tổng thể")
    uncertain_questions: List[int] = Field(..., description="Câu hỏi không chắc chắn")
    processing_time: float = Field(..., description="Thời gian xử lý (giây)")
    success: bool = Field(..., description="Thành công hay không")
    error_message: Optional[str] = Field(None, description="Thông báo lỗi nếu có")

class ScoreStatistics(BaseModel):
    """Thống kê điểm số"""
    average_score: float = Field(..., description="Điểm trung bình")
    median_score: float = Field(..., description="Điểm trung vị")
    min_score: float = Field(..., description="Điểm thấp nhất")
    max_score: float = Field(..., description="Điểm cao nhất")
    std_score: float = Field(..., description="Độ lệch chuẩn")

class GradeDistribution(BaseModel):
    """Phân bố điểm theo mức"""
    excellent: int = Field(..., description="Xuất sắc (>= 8.5)")
    good: int = Field(..., description="Giỏi (7.0-8.4)")
    average: int = Field(..., description="Khá (5.5-6.9)")
    below_average: int = Field(..., description="Trung bình (4.0-5.4)")
    poor: int = Field(..., description="Yếu (< 4.0)")

class QuestionAnalysis(BaseModel):
    """Phân tích một câu hỏi"""
    correct_rate: float = Field(..., description="Tỷ lệ trả lời đúng")
    wrong_rate: float = Field(..., description="Tỷ lệ trả lời sai")
    blank_rate: float = Field(..., description="Tỷ lệ bỏ trống")
    difficulty: str = Field(..., description="Độ khó: easy/medium/hard")

class SummaryStats(BaseModel):
    """Thống kê tổng hợp"""
    total_sheets: int = Field(..., description="Tổng số phiếu")
    successful_sheets: int = Field(..., description="Số phiếu xử lý thành công")
    failed_sheets: int = Field(..., description="Số phiếu xử lý thất bại")
    score_statistics: ScoreStatistics = Field(..., description="Thống kê điểm số")
    grade_distribution: GradeDistribution = Field(..., description="Phân bố điểm")
    answer_analysis: Dict[int, QuestionAnalysis] = Field(..., description="Phân tích từng câu")

class BatchGradingResponse(BaseModel):
    """Response cho chấm điểm hàng loạt"""
    total_sheets: int = Field(..., description="Tổng số phiếu")
    successful_sheets: int = Field(..., description="Số phiếu thành công")
    failed_sheets: int = Field(..., description="Số phiếu thất bại")
    results: List[GradingResultResponse] = Field(..., description="Kết quả từng phiếu")
    summary_stats: SummaryStats = Field(..., description="Thống kê tổng hợp")
    processing_time: float = Field(..., description="Thời gian xử lý tổng (giây)")
    success: bool = Field(..., description="Thành công hay không")
    error_message: Optional[str] = Field(None, description="Thông báo lỗi nếu có")

class TestRequest(BaseModel):
    """Request cho test API"""
    test_image_name: str = Field(default="1.jpeg", description="Tên file ảnh test")
    test_answer_key: str = Field(default="sample_answer_keys.xlsx", description="File đáp án test")

class TestResponse(BaseModel):
    """Response cho test API"""
    message: str = Field(..., description="Thông báo")
    test_result: Optional[GradingResultResponse] = Field(None, description="Kết quả test")
    available_test_files: List[str] = Field(..., description="Danh sách file test có sẵn")
    success: bool = Field(..., description="Thành công hay không")
    error_message: Optional[str] = Field(None, description="Thông báo lỗi nếu có")

class OMRProcessingStatus(BaseModel):
    """Trạng thái xử lý OMR"""
    stage: str = Field(..., description="Giai đoạn hiện tại")
    progress: float = Field(..., description="Tiến độ (0-100)")
    message: str = Field(..., description="Thông báo")
    details: Dict[str, Any] = Field(default_factory=dict, description="Chi tiết bổ sung")

class OMRConfig(BaseModel):
    """Cấu hình OMR"""
    blur_kernel_size: int = Field(default=5, description="Kích thước kernel blur")
    adaptive_threshold_block_size: int = Field(default=11, description="Block size cho adaptive threshold")
    adaptive_threshold_c: int = Field(default=2, description="Constant C cho adaptive threshold")
    min_bubble_area: int = Field(default=50, description="Diện tích bubble tối thiểu")
    max_bubble_area: int = Field(default=200, description="Diện tích bubble tối đa")
    fill_threshold: float = Field(default=0.4, description="Ngưỡng fill ratio")
    min_circularity: float = Field(default=0.3, description="Độ tròn tối thiểu")

class GradingConfig(BaseModel):
    """Cấu hình chấm điểm"""
    omr_config: OMRConfig = Field(default_factory=OMRConfig, description="Cấu hình OMR")
    enable_perspective_correction: bool = Field(default=True, description="Bật perspective correction")
    enable_reference_markers: bool = Field(default=True, description="Bật reference markers")
    confidence_threshold: float = Field(default=0.5, description="Ngưỡng confidence")
    max_processing_time: int = Field(default=300, description="Thời gian xử lý tối đa (giây)")

class ErrorResponse(BaseModel):
    """Response lỗi chuẩn"""
    success: bool = Field(default=False, description="Thành công hay không")
    error_code: str = Field(..., description="Mã lỗi")
    error_message: str = Field(..., description="Thông báo lỗi")
    details: Optional[Dict[str, Any]] = Field(None, description="Chi tiết lỗi")
    timestamp: datetime = Field(default_factory=datetime.now, description="Thời gian lỗi")

class HealthCheckResponse(BaseModel):
    """Response cho health check"""
    status: str = Field(..., description="Trạng thái service")
    version: str = Field(..., description="Phiên bản")
    uptime: float = Field(..., description="Thời gian hoạt động (giây)")
    dependencies: Dict[str, str] = Field(..., description="Trạng thái dependencies")
    timestamp: datetime = Field(default_factory=datetime.now, description="Thời gian check")

# Utility functions for model conversion
def convert_grading_result_to_response(grading_result) -> GradingResultResponse:
    """Convert GradingResult dataclass to Pydantic model"""
    try:
        # Convert answer_details
        answer_details_converted = {}
        for q_num, detail in grading_result.answer_details.items():
            answer_details_converted[q_num] = AnswerDetail(
                student_answer=detail["student_answer"],
                correct_answer=detail["correct_answer"],
                is_correct=detail["is_correct"],
                is_blank=detail["is_blank"],
                confidence=detail["confidence"],
                status=detail["status"]
            )
        
        return GradingResultResponse(
            student_id=grading_result.student_id,
            test_code=grading_result.test_code,
            score=grading_result.score,
            total_questions=grading_result.total_questions,
            correct_answers=grading_result.correct_answers,
            wrong_answers=grading_result.wrong_answers,
            blank_answers=grading_result.blank_answers,
            answers=grading_result.answers,
            correct_answer_keys=grading_result.correct_answer_keys,
            answer_details=answer_details_converted,
            processed_image_base64=grading_result.processed_image_base64,
            confidence_score=grading_result.confidence_score,
            uncertain_questions=grading_result.uncertain_questions,
            processing_time=grading_result.processing_time,
            success=grading_result.success,
            error_message=grading_result.error_message
        )
    except Exception as e:
        # Return error response if conversion fails
        return GradingResultResponse(
            student_id="",
            test_code="",
            score=0.0,
            total_questions=0,
            correct_answers=0,
            wrong_answers=0,
            blank_answers=0,
            answers={},
            correct_answer_keys={},
            answer_details={},
            processed_image_base64="",
            confidence_score=0.0,
            uncertain_questions=[],
            processing_time=0.0,
            success=False,
            error_message=f"Model conversion error: {str(e)}"
        )

def convert_batch_result_to_response(batch_result) -> BatchGradingResponse:
    """Convert BatchGradingResult dataclass to Pydantic model"""
    try:
        # Convert individual results
        results_converted = [
            convert_grading_result_to_response(result) 
            for result in batch_result.results
        ]
        
        # Convert summary stats
        stats = batch_result.summary_stats
        if stats and "score_statistics" in stats:
            score_stats = ScoreStatistics(**stats["score_statistics"])
            grade_dist = GradeDistribution(**stats["grade_distribution"])
            
            # Convert answer analysis
            answer_analysis_converted = {}
            if "answer_analysis" in stats:
                for q_num, analysis in stats["answer_analysis"].items():
                    answer_analysis_converted[q_num] = QuestionAnalysis(**analysis)
            
            summary_stats = SummaryStats(
                total_sheets=stats["total_sheets"],
                successful_sheets=stats["successful_sheets"],
                failed_sheets=stats["failed_sheets"],
                score_statistics=score_stats,
                grade_distribution=grade_dist,
                answer_analysis=answer_analysis_converted
            )
        else:
            # Default empty stats
            summary_stats = SummaryStats(
                total_sheets=batch_result.total_sheets,
                successful_sheets=batch_result.successful_sheets,
                failed_sheets=batch_result.failed_sheets,
                score_statistics=ScoreStatistics(
                    average_score=0, median_score=0, min_score=0, 
                    max_score=0, std_score=0
                ),
                grade_distribution=GradeDistribution(
                    excellent=0, good=0, average=0, below_average=0, poor=0
                ),
                answer_analysis={}
            )
        
        return BatchGradingResponse(
            total_sheets=batch_result.total_sheets,
            successful_sheets=batch_result.successful_sheets,
            failed_sheets=batch_result.failed_sheets,
            results=results_converted,
            summary_stats=summary_stats,
            processing_time=batch_result.processing_time,
            success=batch_result.success,
            error_message=batch_result.error_message
        )
        
    except Exception as e:
        # Return error response if conversion fails
        return BatchGradingResponse(
            total_sheets=0,
            successful_sheets=0,
            failed_sheets=0,
            results=[],
            summary_stats=SummaryStats(
                total_sheets=0, successful_sheets=0, failed_sheets=0,
                score_statistics=ScoreStatistics(
                    average_score=0, median_score=0, min_score=0, 
                    max_score=0, std_score=0
                ),
                grade_distribution=GradeDistribution(
                    excellent=0, good=0, average=0, below_average=0, poor=0
                ),
                answer_analysis={}
            ),
            processing_time=0.0,
            success=False,
            error_message=f"Model conversion error: {str(e)}"
        )
