from fastapi import APIRouter, UploadFile, File, HTTPException, Depends
from fastapi.responses import JSONResponse
from typing import List, Optional
import os
import logging
from datetime import datetime
import asyncio

from app.services.grading_service import batch_grade_all, grading_service
from app.models.grading_models import (
    BatchGradingResponse,
    TestRequest,
    TestResponse,
    GradingResultResponse,
    ErrorResponse,
    HealthCheckResponse,
    convert_batch_result_to_response,
    convert_grading_result_to_response,
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/auto_grading", tags=["Auto Grading - Chấm điểm tự động"])

# Service start time for uptime calculation
service_start_time = datetime.now()


@router.post("/auto", response_model=BatchGradingResponse)
async def auto_grading_endpoint(
    image_files: List[UploadFile] = File(
        ..., description="Danh sách ảnh phiếu trả lời"
    ),
    excel_file: UploadFile = File(..., description="File Excel chứa đáp án"),
):
    """
    API chấm điểm tự động hàng loạt

    - **image_files**: Danh sách file ảnh phiếu trả lời (JPEG, PNG)
    - **excel_file**: File Excel chứa đáp án với cột 'Mã Đề' và các cột câu hỏi (1, 2, 3, ...)

    Returns:
    - Kết quả chấm điểm chi tiết cho từng phiếu
    - Thống kê tổng hợp
    - Phân tích độ khó câu hỏi
    """
    try:
        logger.info(f"Starting auto grading for {len(image_files)} images")

        # Validate input files
        if not image_files:
            raise HTTPException(status_code=400, detail="No image files provided")

        if not excel_file:
            raise HTTPException(status_code=400, detail="No Excel file provided")

        # Validate file types
        allowed_image_types = ["image/jpeg", "image/jpg", "image/png"]
        for img_file in image_files:
            if img_file.content_type not in allowed_image_types:
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid image type: {img_file.content_type}. Allowed: {allowed_image_types}",
                )

        if not excel_file.filename or not excel_file.filename.endswith(
            (".xlsx", ".xls")
        ):
            raise HTTPException(
                status_code=400, detail="Excel file must be .xlsx or .xls format"
            )

        # Process grading
        batch_result = await batch_grade_all(image_files, excel_file)

        # Convert to response model
        response = convert_batch_result_to_response(batch_result)

        logger.info(
            f"Auto grading completed: {response.successful_sheets}/{response.total_sheets} successful"
        )

        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in auto grading endpoint: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.post("/single", response_model=GradingResultResponse)
async def grade_single_sheet(
    image_file: UploadFile = File(..., description="Ảnh phiếu trả lời"),
    excel_file: UploadFile = File(..., description="File Excel chứa đáp án"),
):
    """
    API chấm điểm một phiếu trả lời

    - **image_file**: File ảnh phiếu trả lời (JPEG, PNG)
    - **excel_file**: File Excel chứa đáp án

    Returns:
    - Kết quả chấm điểm chi tiết cho phiếu này
    """
    try:
        logger.info(f"Grading single sheet: {image_file.filename}")

        # Validate input
        allowed_image_types = ["image/jpeg", "image/jpg", "image/png"]
        if image_file.content_type not in allowed_image_types:
            raise HTTPException(
                status_code=400, detail=f"Invalid image type: {image_file.content_type}"
            )

        # Load answer keys
        answer_keys = grading_service.load_answer_keys_from_excel(excel_file)

        # Grade single sheet
        result = grading_service.grade_single_sheet(image_file, answer_keys)

        # Convert to response model
        response = convert_grading_result_to_response(result)

        logger.info(f"Single sheet grading completed: {response.score:.2f}/10")

        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in single sheet grading: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.get("/test", response_model=TestResponse)
async def test_with_sample_data():
    """
    API test với dữ liệu mẫu trong thư mục data/grading

    Returns:
    - Kết quả test với file mẫu
    - Danh sách file test có sẵn
    """
    try:
        import os
        from fastapi import UploadFile
        import io

        # Đường dẫn thư mục test data
        test_data_dir = "data/grading"

        # Kiểm tra thư mục tồn tại
        if not os.path.exists(test_data_dir):
            raise HTTPException(status_code=404, detail="Test data directory not found")

        # Lấy danh sách file có sẵn
        available_files = []
        if os.path.exists(test_data_dir):
            available_files = [
                f
                for f in os.listdir(test_data_dir)
                if f.lower().endswith((".jpg", ".jpeg", ".png", ".xlsx", ".xls"))
            ]

        # Tìm file ảnh và Excel mẫu
        image_files = [
            f for f in available_files if f.lower().endswith((".jpg", ".jpeg", ".png"))
        ]
        excel_files = [
            f for f in available_files if f.lower().endswith((".xlsx", ".xls"))
        ]

        if not image_files:
            return TestResponse(
                message="No test image files found",
                test_result=None,
                available_test_files=available_files,
                success=False,
                error_message="No image files in test directory",
            )

        if not excel_files:
            return TestResponse(
                message="No test Excel files found",
                test_result=None,
                available_test_files=available_files,
                success=False,
                error_message="No Excel files in test directory",
            )

        # Sử dụng file đầu tiên
        test_image_path = os.path.join(test_data_dir, image_files[0])
        test_excel_path = os.path.join(test_data_dir, excel_files[0])

        # Đọc file và tạo UploadFile objects
        with open(test_image_path, "rb") as img_file:
            image_content = img_file.read()

        with open(test_excel_path, "rb") as excel_file:
            excel_content = excel_file.read()

        # Tạo UploadFile objects
        image_upload = UploadFile(
            filename=image_files[0], file=io.BytesIO(image_content)
        )

        excel_upload = UploadFile(
            filename=excel_files[0], file=io.BytesIO(excel_content)
        )

        # Load answer keys
        answer_keys = grading_service.load_answer_keys_from_excel(excel_upload)

        # Reset file pointer
        image_upload.file.seek(0)

        # Grade single sheet
        result = grading_service.grade_single_sheet(image_upload, answer_keys)

        # Convert to response model
        test_result = convert_grading_result_to_response(result)

        logger.info(f"Test completed with sample data: {test_result.score:.2f}/10")

        return TestResponse(
            message=f"Test completed successfully with {image_files[0]} and {excel_files[0]}",
            test_result=test_result,
            available_test_files=available_files,
            success=True,
            error_message=None,
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in test endpoint: {e}")
        return TestResponse(
            message="Test failed",
            test_result=None,
            available_test_files=[],
            success=False,
            error_message=str(e),
        )


@router.get("/health", response_model=HealthCheckResponse)
async def health_check():
    """
    Health check endpoint để kiểm tra trạng thái service

    Returns:
    - Trạng thái service
    - Thời gian hoạt động
    - Trạng thái dependencies
    """
    try:
        import cv2
        import pandas as pd
        import numpy as np

        # Tính uptime
        uptime = (datetime.now() - service_start_time).total_seconds()

        # Kiểm tra dependencies
        dependencies = {
            "opencv": "OK" if cv2.__version__ else "ERROR",
            "pandas": "OK" if pd.__version__ else "ERROR",
            "numpy": "OK" if np.__version__ else "ERROR",
            "omr_processor": "OK" if grading_service.omr_processor else "ERROR",
            "grading_service": "OK",
        }

        # Kiểm tra test data directory
        import os

        test_data_exists = os.path.exists("data/grading")
        dependencies["test_data_directory"] = "OK" if test_data_exists else "WARNING"

        return HealthCheckResponse(
            status="healthy",
            version="1.0.0",
            uptime=uptime,
            dependencies=dependencies,
            timestamp=datetime.now(),
        )

    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return HealthCheckResponse(
            status="unhealthy",
            version="1.0.0",
            uptime=0.0,
            dependencies={"error": str(e)},
            timestamp=datetime.now(),
        )
